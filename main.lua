local Bot = require('core/bot')

local config = {
    host = "127.0.0.1",
    port = 6700
}

local bot = Bot:new(config)

-- 注册消息处理
bot.events:on('message', function(data)
    if data.message == "ping" then
        bot:sendMessage({
            group_id = data.group_id,
            message = "pong"
        }, data._client) -- 回复给发送消息的客户端
    end
end)

-- 启动Bot服务器
bot:start()

print("Bot服务器正在运行...")
print("等待OneBot客户端连接到 ws://" .. config.host .. ":" .. config.port)
